<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>TreeTable 性能测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="./css/layui.css">
    <style>
        .performance-info {
            margin: 20px 0;
            padding: 15px;
            background: #f8f8f8;
            border-radius: 5px;
        }
        .test-controls {
            margin: 20px 0;
            text-align: center;
        }
        .test-controls button {
            margin: 0 10px;
        }
        .performance-metrics {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .metric {
            text-align: center;
            padding: 10px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="layui-container">
        <div class="layui-row">
            <div class="layui-col-md12">
                <h1>TreeTable 性能测试</h1>
                
                <div class="performance-info">
                    <h3>性能优化说明</h3>
                    <ul>
                        <li><strong>懒渲染</strong>：只渲染可见的节点，大幅减少DOM操作</li>
                        <li><strong>批量DOM操作</strong>：使用requestAnimationFrame批量处理DOM更新</li>
                        <li><strong>防抖优化</strong>：对频繁操作进行防抖处理</li>
                        <li><strong>DOM缓存</strong>：缓存常用的DOM查询结果</li>
                        <li><strong>虚拟滚动</strong>：支持大数据量的虚拟滚动（可选）</li>
                    </ul>
                </div>

                <div class="test-controls">
                    <button class="layui-btn" onclick="generateSmallData()">生成1000条数据</button>
                    <button class="layui-btn" onclick="generateMediumData()">生成5000条数据</button>
                    <button class="layui-btn" onclick="generateLargeData()">生成10000条数据</button>
                    <button class="layui-btn layui-btn-primary" onclick="clearData()">清空数据</button>
                </div>

                <div class="performance-metrics">
                    <div class="metric">
                        <div class="metric-value" id="renderTime">0</div>
                        <div class="metric-label">渲染时间 (ms)</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="nodeCount">0</div>
                        <div class="metric-label">节点数量</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="memoryUsage">0</div>
                        <div class="metric-label">内存使用 (MB)</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value" id="domNodes">0</div>
                        <div class="metric-label">DOM节点数</div>
                    </div>
                </div>

                <table id="treeTable" lay-filter="treeTable"></table>
            </div>
        </div>
    </div>

    <script src="layui.js"></script>
    <script src="treeTable-lay.js"></script>
    <script>
        layui.use(['treeTable'], function() {
            var treeTable = layui.treeTable;
            var table;

            // 生成测试数据
            function generateTestData(count, maxDepth, currentDepth) {
                currentDepth = currentDepth || 0;
                var data = [];
                var itemsPerLevel = Math.ceil(count / Math.pow(2, currentDepth));
                
                for (var i = 0; i < itemsPerLevel && data.length < count; i++) {
                    var id = 'node_' + currentDepth + '_' + i;
                    var item = {
                        id: id,
                        name: '节点 ' + id,
                        type: currentDepth === 0 ? '根节点' : '子节点',
                        status: Math.random() > 0.5 ? '启用' : '禁用',
                        createTime: new Date().toLocaleString(),
                        description: '这是一个测试节点，用于性能测试 ' + id
                    };

                    // 递归生成子节点
                    if (currentDepth < maxDepth && Math.random() > 0.3) {
                        var childCount = Math.min(Math.floor(Math.random() * 5) + 1, count - data.length);
                        if (childCount > 0) {
                            item.children = generateTestData(childCount, maxDepth, currentDepth + 1);
                        }
                    }

                    data.push(item);
                }
                
                return data;
            }

            // 性能监控
            function measurePerformance(callback) {
                var startTime = performance.now();
                var startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                
                callback();
                
                setTimeout(function() {
                    var endTime = performance.now();
                    var endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
                    var renderTime = Math.round(endTime - startTime);
                    var memoryUsage = Math.round((endMemory - startMemory) / 1024 / 1024 * 100) / 100;
                    var domNodes = document.querySelectorAll('*').length;
                    
                    document.getElementById('renderTime').textContent = renderTime;
                    document.getElementById('memoryUsage').textContent = memoryUsage;
                    document.getElementById('domNodes').textContent = domNodes;
                }, 100);
            }

            // 渲染表格
            function renderTable(data) {
                measurePerformance(function() {
                    table = treeTable.render({
                        elem: '#treeTable',
                        data: data,
                        // 启用性能优化
                        performance: {
                            lazyRender: true,
                            batchSize: 50,
                            debounceTime: 16,
                            cacheSize: 500
                        },
                        cols: [[
                            {type: 'checkbox'},
                            {field: 'name', title: '名称', width: 200},
                            {field: 'type', title: '类型', width: 100},
                            {field: 'status', title: '状态', width: 100},
                            {field: 'createTime', title: '创建时间', width: 180},
                            {field: 'description', title: '描述', minWidth: 200}
                        ]],
                        tree: {
                            iconIndex: 1,
                            idName: 'id',
                            childName: 'children'
                        }
                    });
                    
                    document.getElementById('nodeCount').textContent = countNodes(data);
                });
            }

            // 计算节点总数
            function countNodes(data) {
                var count = 0;
                function traverse(nodes) {
                    count += nodes.length;
                    nodes.forEach(function(node) {
                        if (node.children) {
                            traverse(node.children);
                        }
                    });
                }
                traverse(data);
                return count;
            }

            // 全局函数
            window.generateSmallData = function() {
                var data = generateTestData(1000, 3);
                renderTable(data);
            };

            window.generateMediumData = function() {
                var data = generateTestData(5000, 4);
                renderTable(data);
            };

            window.generateLargeData = function() {
                var data = generateTestData(10000, 5);
                renderTable(data);
            };

            window.clearData = function() {
                if (table) {
                    table.reload({data: []});
                    document.getElementById('nodeCount').textContent = '0';
                    document.getElementById('renderTime').textContent = '0';
                    document.getElementById('memoryUsage').textContent = '0';
                    document.getElementById('domNodes').textContent = document.querySelectorAll('*').length;
                }
            };

            // 初始化小数据集
            generateSmallData();
        });
    </script>
</body>
</html>
