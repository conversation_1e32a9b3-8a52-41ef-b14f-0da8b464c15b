<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>TreeTable 优化版本示例</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="./css/layui.css">
</head>
<body>
    <div class="layui-container">
        <div class="layui-row">
            <div class="layui-col-md12">
                <h1>TreeTable 性能优化版本示例</h1>
                
                <div class="layui-card">
                    <div class="layui-card-header">基本使用</div>
                    <div class="layui-card-body">
                        <table id="basicTable" lay-filter="basicTable"></table>
                    </div>
                </div>

                <div class="layui-card" style="margin-top: 20px;">
                    <div class="layui-card-header">大数据量优化版本</div>
                    <div class="layui-card-body">
                        <div style="margin-bottom: 15px;">
                            <button class="layui-btn layui-btn-sm" onclick="loadLargeData()">加载大数据</button>
                            <button class="layui-btn layui-btn-sm layui-btn-primary" onclick="clearLargeData()">清空数据</button>
                            <span style="margin-left: 20px; color: #666;">
                                渲染时间: <span id="renderTime">0</span>ms | 
                                节点数: <span id="nodeCount">0</span>
                            </span>
                        </div>
                        <table id="optimizedTable" lay-filter="optimizedTable"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="layui.js"></script>
    <script src="treeTable-lay.js"></script>
    <script>
        layui.use(['treeTable'], function() {
            var treeTable = layui.treeTable;

            // 基本示例数据
            var basicData = [
                {
                    id: 1,
                    name: '部门管理',
                    type: '模块',
                    status: '启用',
                    children: [
                        {
                            id: 11,
                            name: '部门列表',
                            type: '页面',
                            status: '启用',
                            children: [
                                {id: 111, name: '查看', type: '权限', status: '启用'},
                                {id: 112, name: '新增', type: '权限', status: '启用'},
                                {id: 113, name: '修改', type: '权限', status: '启用'},
                                {id: 114, name: '删除', type: '权限', status: '禁用'}
                            ]
                        },
                        {
                            id: 12,
                            name: '部门设置',
                            type: '页面',
                            status: '启用'
                        }
                    ]
                },
                {
                    id: 2,
                    name: '用户管理',
                    type: '模块',
                    status: '启用',
                    children: [
                        {
                            id: 21,
                            name: '用户列表',
                            type: '页面',
                            status: '启用',
                            children: [
                                {id: 211, name: '查看', type: '权限', status: '启用'},
                                {id: 212, name: '新增', type: '权限', status: '启用'},
                                {id: 213, name: '修改', type: '权限', status: '启用'},
                                {id: 214, name: '删除', type: '权限', status: '启用'}
                            ]
                        },
                        {
                            id: 22,
                            name: '角色管理',
                            type: '页面',
                            status: '启用',
                            children: [
                                {id: 221, name: '查看', type: '权限', status: '启用'},
                                {id: 222, name: '分配权限', type: '权限', status: '启用'}
                            ]
                        }
                    ]
                }
            ];

            // 渲染基本表格
            treeTable.render({
                elem: '#basicTable',
                data: basicData,
                cols: [[
                    {type: 'checkbox'},
                    {field: 'name', title: '名称', width: 200},
                    {field: 'type', title: '类型', width: 100},
                    {field: 'status', title: '状态', width: 100, templet: function(d) {
                        return d.status === '启用' ? 
                            '<span class="layui-badge layui-bg-green">启用</span>' : 
                            '<span class="layui-badge">禁用</span>';
                    }},
                    {title: '操作', width: 150, toolbar: '#operationTpl'}
                ]],
                tree: {
                    iconIndex: 1,
                    idName: 'id',
                    childName: 'children'
                }
            });

            // 生成大数据量测试数据
            function generateLargeData(count) {
                var data = [];
                var startTime = performance.now();
                
                for (var i = 0; i < count / 10; i++) {
                    var parentNode = {
                        id: 'parent_' + i,
                        name: '父节点 ' + i,
                        type: '模块',
                        status: Math.random() > 0.5 ? '启用' : '禁用',
                        children: []
                    };
                    
                    // 每个父节点生成10个子节点
                    for (var j = 0; j < 10; j++) {
                        var childNode = {
                            id: 'child_' + i + '_' + j,
                            name: '子节点 ' + i + '_' + j,
                            type: '页面',
                            status: Math.random() > 0.5 ? '启用' : '禁用'
                        };
                        
                        // 部分子节点再生成孙节点
                        if (Math.random() > 0.7) {
                            childNode.children = [];
                            for (var k = 0; k < 3; k++) {
                                childNode.children.push({
                                    id: 'grandchild_' + i + '_' + j + '_' + k,
                                    name: '孙节点 ' + i + '_' + j + '_' + k,
                                    type: '权限',
                                    status: Math.random() > 0.5 ? '启用' : '禁用'
                                });
                            }
                        }
                        
                        parentNode.children.push(childNode);
                    }
                    
                    data.push(parentNode);
                }
                
                var generateTime = performance.now() - startTime;
                console.log('数据生成时间:', Math.round(generateTime), 'ms');
                
                return data;
            }

            // 计算节点总数
            function countNodes(data) {
                var count = 0;
                function traverse(nodes) {
                    count += nodes.length;
                    nodes.forEach(function(node) {
                        if (node.children) {
                            traverse(node.children);
                        }
                    });
                }
                traverse(data);
                return count;
            }

            // 加载大数据量
            window.loadLargeData = function() {
                var startTime = performance.now();
                var largeData = generateLargeData(5000);
                
                // 使用性能优化版本渲染
                treeTable.render({
                    elem: '#optimizedTable',
                    data: largeData,
                    // 启用性能优化
                    performance: {
                        lazyRender: true,
                        batchSize: 50,
                        debounceTime: 16,
                        cacheSize: 500
                    },
                    cols: [[
                        {type: 'checkbox'},
                        {field: 'name', title: '名称', width: 250},
                        {field: 'type', title: '类型', width: 100},
                        {field: 'status', title: '状态', width: 100, templet: function(d) {
                            return d.status === '启用' ? 
                                '<span class="layui-badge layui-bg-green">启用</span>' : 
                                '<span class="layui-badge">禁用</span>';
                        }},
                        {title: '操作', width: 150, toolbar: '#operationTpl'}
                    ]],
                    tree: {
                        iconIndex: 1,
                        idName: 'id',
                        childName: 'children'
                    },
                    done: function() {
                        var endTime = performance.now();
                        var renderTime = Math.round(endTime - startTime);
                        var nodeCount = countNodes(largeData);
                        
                        document.getElementById('renderTime').textContent = renderTime;
                        document.getElementById('nodeCount').textContent = nodeCount;
                    }
                });
            };

            // 清空大数据
            window.clearLargeData = function() {
                treeTable.reload('optimizedTable', {data: []});
                document.getElementById('renderTime').textContent = '0';
                document.getElementById('nodeCount').textContent = '0';
            };
        });
    </script>

    <!-- 操作列模板 -->
    <script type="text/html" id="operationTpl">
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
    </script>
</body>
</html>
